#!/usr/bin/env python
"""
Debug script for LinkedIn authentication issues.
Run this script to test LinkedIn OAuth2 configuration.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from allauth.socialaccount.models import SocialApp
from django.contrib.sites.models import Site
from apps.user.api.v1.serializers.social_auth_serializer import LinkedInSocialLoginSerializer
from dj_rest_auth.registration.serializers import SocialLoginSerializer


def check_linkedin_configuration():
    """Check LinkedIn OAuth2 configuration"""
    print("=== LinkedIn OAuth2 Configuration Check ===\n")
    
    # Check if LinkedIn social app is configured
    try:
        linkedin_app = SocialApp.objects.get(provider='linkedin_oauth2')
        print(f"✓ LinkedIn Social App found:")
        print(f"  - Name: {linkedin_app.name}")
        print(f"  - Client ID: {linkedin_app.client_id[:10]}..." if linkedin_app.client_id else "  - Client ID: Not set")
        print(f"  - Client Secret: {'Set' if linkedin_app.secret else 'Not set'}")
        print(f"  - Sites: {[site.domain for site in linkedin_app.sites.all()]}")
    except SocialApp.DoesNotExist:
        print("✗ LinkedIn Social App not found in database")
        print("  Please add a LinkedIn Social App in Django admin:")
        print("  1. Go to /admin/socialaccount/socialapp/")
        print("  2. Add new Social Application")
        print("  3. Provider: LinkedIn OAuth2")
        print("  4. Add your LinkedIn app credentials")
        return False
    
    # Check site configuration
    try:
        site = Site.objects.get(pk=1)
        print(f"\n✓ Site configuration:")
        print(f"  - Domain: {site.domain}")
        print(f"  - Name: {site.name}")
    except Site.DoesNotExist:
        print("\n✗ Default site not found")
        return False
    
    return True


def test_serializer_validation():
    """Test LinkedIn serializer validation"""
    print("\n=== LinkedIn Serializer Test ===\n")
    
    # Test with empty data
    serializer = LinkedInSocialLoginSerializer(data={})
    if not serializer.is_valid():
        print("✓ Empty data validation:")
        print(f"  - Errors: {serializer.errors}")
    
    # Test with code
    test_data = {'code': 'test_code_123'}
    serializer = LinkedInSocialLoginSerializer(data=test_data)
    print(f"\n✓ Test data validation (code only):")
    print(f"  - Data: {test_data}")
    print(f"  - Valid: {serializer.is_valid()}")
    if not serializer.is_valid():
        print(f"  - Errors: {serializer.errors}")
    
    # Test with access_token
    test_data = {'access_token': 'test_token_123'}
    serializer = LinkedInSocialLoginSerializer(data=test_data)
    print(f"\n✓ Test data validation (access_token only):")
    print(f"  - Data: {test_data}")
    print(f"  - Valid: {serializer.is_valid()}")
    if not serializer.is_valid():
        print(f"  - Errors: {serializer.errors}")


def check_environment_variables():
    """Check environment variables"""
    print("\n=== Environment Variables Check ===\n")
    
    from django.conf import settings
    
    linkedin_url = getattr(settings, 'LINKEDIN_REDIRECT_URL', None)
    print(f"✓ LINKEDIN_REDIRECT_URL: {linkedin_url}")
    
    # Check if allauth is properly configured
    socialaccount_providers = getattr(settings, 'SOCIALACCOUNT_PROVIDERS', {})
    linkedin_config = socialaccount_providers.get('linkedin_oauth2', {})
    print(f"✓ LinkedIn provider config: {linkedin_config}")
    
    # Check installed apps
    installed_apps = getattr(settings, 'INSTALLED_APPS', [])
    linkedin_installed = 'allauth.socialaccount.providers.linkedin_oauth2' in installed_apps
    print(f"✓ LinkedIn provider installed: {linkedin_installed}")


def main():
    """Main debug function"""
    print("LinkedIn OAuth2 Debug Script")
    print("=" * 50)
    
    try:
        # Check configuration
        config_ok = check_linkedin_configuration()
        
        # Check environment
        check_environment_variables()
        
        # Test serializer
        test_serializer_validation()
        
        print("\n" + "=" * 50)
        if config_ok:
            print("✓ Basic configuration looks good!")
            print("\nNext steps to debug the 'Incorrect value' error:")
            print("1. Check the LinkedIn app credentials in Django admin")
            print("2. Verify the redirect URL matches your LinkedIn app settings")
            print("3. Test with a real authorization code from LinkedIn")
            print("4. Check the application logs for more detailed error messages")
        else:
            print("✗ Configuration issues found. Please fix them first.")
            
    except Exception as e:
        print(f"\n✗ Error running debug script: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
