from dj_rest_auth.registration.serializers import SocialLoginSerializer
from dxh_libraries.rest_framework import serializers
from dxh_libraries.translation import gettext_lazy as _


class CustomSocialLoginSerializer(SocialLoginSerializer):
    """
    Custom social login serializer with better validation and error handling
    """
    code = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text=_("Authorization code from OAuth provider")
    )
    access_token = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text=_("Access token from OAuth provider")
    )

    def validate(self, attrs):
        """
        Custom validation to provide better error messages
        """
        # Check if either code or access_token is provided
        code = attrs.get('code')
        access_token = attrs.get('access_token')

        if not code and not access_token:
            raise serializers.ValidationError({
                'non_field_errors': [_('Either authorization code or access token is required.')]
            })

        try:
            # Only call parent validation if we have a request context
            if hasattr(self, 'context') and self.context.get('request'):
                return super().validate(attrs)
            else:
                # If no request context, just return the attrs for now
                # The actual validation will happen in the view
                return attrs
        except AttributeError as e:
            # Handle missing request context
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Social login validation error (missing context): {str(e)}")

            # Return attrs if it's just a context issue
            if "'NoneType' object has no attribute '_request'" in str(e):
                return attrs

            raise serializers.ValidationError({
                'non_field_errors': [_('Invalid authorization credentials. Please try again.')]
            })
        except Exception as e:
            # Log the original error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Social login validation error: {str(e)}")

            # Check for specific error types
            error_str = str(e).lower()
            if 'incorrect value' in error_str:
                raise serializers.ValidationError({
                    'non_field_errors': [_('Invalid authorization code or access token. Please try authenticating again.')]
                })
            elif 'invalid' in error_str:
                raise serializers.ValidationError({
                    'non_field_errors': [_('Authentication failed. Please check your credentials and try again.')]
                })

            # Provide a more user-friendly error message
            raise serializers.ValidationError({
                'non_field_errors': [_('Authentication error occurred. Please try again.')]
            })


class LinkedInSocialLoginSerializer(CustomSocialLoginSerializer):
    """
    LinkedIn-specific social login serializer
    """
    
    def validate(self, attrs):
        """
        LinkedIn-specific validation
        """
        try:
            return super().validate(attrs)
        except serializers.ValidationError as e:
            # Add LinkedIn-specific error handling
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"LinkedIn social login validation error: {str(e)}")
            
            # Check if it's a LinkedIn-specific error
            error_detail = str(e)
            if 'linkedin' in error_detail.lower() or 'scope' in error_detail.lower():
                raise serializers.ValidationError({
                    'non_field_errors': [_('LinkedIn authentication failed. Please ensure you have granted the required permissions.')]
                })
            
            raise e
